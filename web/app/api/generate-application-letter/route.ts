export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase-server';
import { generateStructuredLetterData } from '@/utils/ai-generators/structuredLetterGenerator';
import { createApiHandler } from '@/utils/apiErrorHandler';
import { captureApiError } from '@/utils/errorMonitoring';
import { trackApiUsage } from '@/lib/mixpanel-server';
import { getTemplateById } from '@/utils/letter-templates/applicationLetterTemplates';
import { fillLetterTemplate } from '@/utils/letter-template-engine';
import { convertToLetterTemplateData, validateStructuredLetterData, convertTemplateDataToPlainText } from '@/types/letter-structured';

async function handleRequest(request: NextRequest, _context?: { params: Record<string, string | string[]> }): Promise<NextResponse> {
  // Start timing the request
  const startTime = Date.now();
  let userId: string | undefined;
  
  
  let formData = new FormData();
  try {
    formData = await request.formData();
    
    const jobDescription = formData.get('jobDescription') as string | null;
    const jobImage = formData.get('jobImage') as File | null;
    const unauthenticatedResumeFile = formData.get('unauthenticatedResumeFile') as File | null;
    const unauthenticatedResumeFileName = formData.get('unauthenticatedResumeFileName') as string | null;
    const templateId = formData.get('templateId') as string | 'plain-text';
    const editedLetterText = formData.get('editedLetterText') as string | null;
    const existingLetterId = formData.get('existingLetterId') as string | null;
    
    if (!jobDescription && !jobImage) {
      return NextResponse.json({
        error: 'Deskripsi pekerjaan dan gambar lowongan diperlukan' 
      }, { status: 400 });
    }
    
    // Get access token from request header
    const accessToken = request.headers.get('x-supabase-auth');

    let buffer: ArrayBuffer;
    let fileName: string;

    if (unauthenticatedResumeFile && unauthenticatedResumeFileName) {
      buffer = await unauthenticatedResumeFile.arrayBuffer();
      fileName = unauthenticatedResumeFileName.toLowerCase();
    } else if (accessToken) {
      // Create Supabase client
      const supabase = await createClient();
          
      // Get user with the provided token
      const { data: { user }, error: userError } = await supabase.auth.getUser(accessToken);

      if (userError || !user) {
        console.error('Error getting user with token:', userError);
        return NextResponse.json({ 
          error: 'Anda harus login untuk menggunakan fitur ini' 
        }, { status: 401 });
      }
      userId = user.id;

      // Get resume data from Supabase storage
      const { data: profile, error: fetchError } = await supabase
        .from('profiles')
        .select('resume_file_name, tokens')
        .eq('id', userId)
        .single();

      if (fetchError || !profile) {
        return NextResponse.json({ 
          error: 'Resume tidak ditemukan. Harap unggah resume terlebih dahulu.' 
        }, { status: 404 });
      }

      // Check token balance against template cost (skip for edit operations)
      const selectedTemplate = getTemplateById(templateId);
      const requiredTokens = selectedTemplate?.tokenCost ?? 0;
      const currentTokens = (profile.tokens as number | null) ?? 0;

      // Only check token balance for new generations, not for edits
      if (!editedLetterText && requiredTokens > 0 && currentTokens < requiredTokens) {
        return NextResponse.json({
          error: 'Token Anda tidak cukup untuk menggunakan template ini. Silakan top up token Anda.'
        }, { status: 402 });
      }

      // Get the resume file from storage
      const { data: resumeFile, error: storageError } = await supabase.storage
        .from('resumes')
        .download(profile.resume_file_name);

      if (storageError || !resumeFile) {
        captureApiError('generate-application-letter', storageError || 'Resume file access error', {
          userId,
          type: 'resume_storage_access',
          resumeFileName: profile.resume_file_name
        });
        return NextResponse.json({ 
          error: 'Tidak dapat mengakses file resume. Harap unggah ulang resume Anda.' 
        }, { status: 500 });
      }

      // Convert the file to buffer and determine correct mime type
      buffer = await resumeFile.arrayBuffer();
      fileName = profile.resume_file_name.toLowerCase();
    } else {
      return NextResponse.json({ 
        error: 'Resume tidak ditemukan. Harap unggah resume terlebih dahulu.' 
      }, { status: 404 });
    }
    
    // Get the correct mime type based on file extension
    let mimeType: string;
    if (fileName.endsWith('.pdf')) {
      mimeType = 'application/pdf';
    } else if (fileName.endsWith('.docx')) {
      mimeType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
    } else if (fileName.endsWith('.png')) {
      mimeType = 'image/png';
    } else if (fileName.endsWith('.jpg') || fileName.endsWith('.jpeg')) {
      mimeType = 'image/jpeg';
    } else {
      mimeType = 'text/plain';
    }
    
    // Prepare job image if it exists
    let jobImageData: { buffer: ArrayBuffer, mimeType: string } | undefined;
    if (jobImage) {
      const jobImageBuffer = await jobImage.arrayBuffer();
      jobImageData = { 
        buffer: jobImageBuffer, 
        mimeType: jobImage.type 
      };
    }

    try {
      // Get selected template
      const selectedTemplate = getTemplateById(templateId);
      if (!selectedTemplate) {
        return NextResponse.json({
          error: 'Template tidak ditemukan'
        }, { status: 404 });
      }

      // Generate structured data using AI
      const structuredData = await generateStructuredLetterData(
        { buffer, mimeType },
        jobDescription || undefined,
        jobImageData,
        {
          templateId,
          language: 'id', // Default to Indonesian
          extractCompanyFromJob: true,
          includeAttachments: true
        }
      );

      // Validate structured data
      const validation = validateStructuredLetterData(structuredData);
      if (!validation.isValid) {
        console.warn('Generated structured data validation failed:', validation.missingFields);
        // Continue with warnings - the fillLetterTemplate will handle missing fields
      }

      let finalHtml = '';
      
      try {
        finalHtml = fillLetterTemplate(selectedTemplate, structuredData);
      } catch (templateError) {
        console.error('Template rendering error:', templateError);
        return NextResponse.json({
          error: 'Gagal membuat desain surat. Silakan coba template lain.'
        }, { status: 500 });
      }

      // Save to database if user is authenticated
      let letterId: string | undefined;
      if (userId) {
        try {
          const supabase = await createClient();

          // Deduct tokens for template usage
          if (selectedTemplate.tokenCost && selectedTemplate.tokenCost > 0) {
            // First get current token balance
            const { data: profile } = await supabase
              .from('profiles')
              .select('tokens')
              .eq('id', userId)
              .single();

            if (profile && profile.tokens >= selectedTemplate.tokenCost) {
              const { error: tokenError } = await supabase
                .from('profiles')
                .update({
                  tokens: profile.tokens - selectedTemplate.tokenCost
                })
                .eq('id', userId);

              if (tokenError) {
                console.error('Error deducting tokens:', tokenError);
                // Continue anyway - don't fail the generation for token errors
              }
            }
          }

          const templateData = convertToLetterTemplateData(structuredData);
          const plainText = convertTemplateDataToPlainText(templateData);

          // Save letter to database
          const letterData = {
            user_id: userId,
            structured_data: structuredData,
            design_html: finalHtml || null,
            template_id: templateId,
            plain_text: plainText,
          };

          if (existingLetterId && editedLetterText) {
            // Update existing letter
            const { data: savedLetter, error: saveError } = await supabase
              .from('letters')
              .update(letterData)
              .eq('id', existingLetterId)
              .eq('user_id', userId)
              .select('id')
              .single();

            if (saveError) {
              console.error('Error updating letter:', saveError);
            } else {
              letterId = savedLetter?.id;
            }
          } else {
            // Create new letter
            const { data: savedLetter, error: saveError } = await supabase
              .from('letters')
              .insert(letterData)
              .select('id')
              .single();

            if (saveError) {
              console.error('Error saving letter:', saveError);
            } else {
              letterId = savedLetter?.id;
            }
          }
        } catch (dbError) {
          console.error('Database error:', dbError);
          // Continue - don't fail generation for DB errors
        }
      }

      // Calculate request duration
      const duration = Date.now() - startTime;

      // Track successful API usage
      trackApiUsage(
        'generate-application-letter-structured',
        'success',
        duration,
        {
          has_job_description: !!jobDescription,
          has_job_image: !!jobImage,
          is_authenticated: !!userId,
          resume_type: fileName.split('.').pop(),
          template_id: templateId,
          has_structured_data: true
        },
        userId
      );

      return NextResponse.json({
        success: true,
        data: {
          structuredData,
          design: finalHtml || null,
          templateId,
          letterId,
        }
      });

    } catch (error) {
      console.error('Error in structured generation:', error);
      
      // Calculate request duration even for errors
      const duration = Date.now() - startTime;
      
      // Track failed API usage
      trackApiUsage(
        'generate-application-letter-structured',
        'error',
        duration,
        {
          error_message: error instanceof Error ? error.message : 'Unknown error',
          is_authenticated: !!userId,
          template_id: templateId
        },
        userId
      );

      return NextResponse.json({
        success: false,
        error: 'Gagal membuat surat lamaran. Silakan coba lagi.'
      }, { status: 500 });
    }
  } catch (error) {
    console.error('Error generating application letter:', error);
    
    // Calculate request duration even for errors
    const duration = Date.now() - startTime;
    
    // Track failed API usage in Mixpanel
    trackApiUsage(
      'generate-application-letter', 
      'error',
      duration,
      {
        error_message: error instanceof Error ? error.message : 'Unknown error',
        is_authenticated: !!userId
      },
      userId
    );
    
    // Capture error details with Rollbar
    captureApiError('generate-application-letter', error, {
      requestUrl: request.url
    });
    
    return NextResponse.json({
      success: false,
      error: 'Gagal membuat surat lamaran. Silakan coba lagi.'
    }, { status: 500 });
  }
}

// Export the handler with error reporting wrapper
export const POST = createApiHandler('generate-application-letter', handleRequest);
